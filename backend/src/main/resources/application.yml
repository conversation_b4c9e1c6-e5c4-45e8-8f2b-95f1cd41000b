# ===================================================================
# BASE (DEFAULT) PROFILE
# Defines the structure and default values for all properties.
# ===================================================================
spring:
  application:
    name: ecommerce_backend
  servlet:
    multipart:
      max-file-size: 8MB
      max-request-size: 8MB
  # Default datasource and JPA settings
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    # URL, username, password will be overridden by active profiles
  jpa:
    properties:
      hibernate:
        format_sql: true
    # ddl-auto and show-sql are best set per-profile
  # RabbitMQ connection details (host will be overridden)
  rabbitmq:
    host: localhost # Default for local development
    port: 5672
    listener:
      simple:
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
  # Springdoc / OpenAPI settings
  springdoc:
    swagger-ui:
      path: /swagger-ui.html
      tags-sorter: alpha
      operations-sorter: method
      groups-order: asc
      enabled: true
    api-docs:
      path: /api-docs
      enabled: true
  flyway:
    default-schema: ecommerce_dev

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: prometheus,health,info,metrics
  endpoint:
    health:
      show-details: always

# Server port
server:
  port: 8080

# ===================================================================
# CUSTOM APPLICATION PROPERTIES (GROUPED UNDER 'app')
# This is a best practice to separate your config from Spring's.
# ===================================================================
app:
  # --- NEW: RabbitMQ topology configuration ---
  rabbitmq:
    email:
      exchange: email.exchange
      queue: email.queue
      routing-key: email.routing.key
    email-notification:
      exchange: email.notification.exchange
      queue: email.notification.queue
      routing-key: email.notification.routing.key
    checkout-session:
      exchange: checkout.session.exchange
      queue: checkout.session.queue
      routing-key: checkout.session.routing.key
    checkout-session-response:
      exchange: checkout.session.response.exchange
      queue: checkout.session.response.queue
      routing-key: checkout.session.response.routing-key
    payment-options-request:
      exchange: "payment.events"
      routing-key: "payment.options.request" # Use kebab-case
    payment-options-reply:
      queue: "payment.options.reply.q"
      exchange: "payment.events"
      routing-key: "payment.options.reply" # Use kebab-case

  # --- MOVED: Storage configuration ---
  storage:
    location: ${STORAGE_LOCATION:${user.home}/ecommerce/storage} # A more sensible default
    base-url: ${STORAGE_BASE_URL:/api/v1/files}
    cleanup-schedule: "0 0 1 * * *" # Default: 1 AM daily
    secret-salt: ${STORAGE_SECRET_SALT}

  # --- MOVED: JWT configuration ---
  jwt:
    secret: ${JWT_SECRET}
    expiration-ms: 900000  # 15 minutes
    refresh-token-expiration-ms: ******** # 24 hours

  # --- MOVED: Other custom properties ---
  payment-service:
    url: ${PAYMENT_SERVICE_URL:http://localhost:8010}
  cleanup:
    cron: "0 0 * * * *"  # Default: every hour
    batch-size: 1000
  payment:
    # This map links a user-chosen PaymentMethod to a specific Gateway implementation key.
    method-to-gateway-mapping:
      CREDIT_CARD: STRIPE_GATEWAY
      BANK_TRANSFER: STRIPE_GATEWAY
      BLIK: STRIPE_GATEWAY
      # When you add PayPal, you'll add:
      # PAYPAL: PAYPAL_GATEWAY

---
# ===================================================================
# DEVELOPMENT PROFILE (dev)
# For local container-based development (e.g., Docker Compose)
# ===================================================================
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: ${SPRING_DATASOURCE_URL} # e.g., *************************************
    username: ${DB_USERNAME:devuser}
    password: ${DB_PASSWORD}
  flyway:
    url: ${spring.datasource.url} # Re-use the datasource URL
    user: ${spring.datasource.username}
    password: ${spring.datasource.password}
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
  rabbitmq:
    host: ${SPRING_RABBITMQ_HOST:rabbitmq}
    username: ${SPRING_RABBITMQ_USERNAME}
    password: ${SPRING_RABBITMQ_PASSWORD}
  logging:
    level:
      com.rj.ecommerce_backend: DEBUG
      org.hibernate.SQL: DEBUG

app:
  storage:
    location: ${DEV_STORAGE_PATH:/tmp/ecommerce/product-images}
    cleanup-schedule: "0 * * * * *"  # Override to run hourly for testing

---
# ===================================================================
# LOCAL PROFILE (local)
# For running the application on your local machine without containers
# ===================================================================
spring:
  config:
    activate:
      on-profile: local
  datasource:
    url: **********************:${DB_PORT:3306}/${DB_NAME:ecommerce_dev}?createDatabaseIfNotExist=true
    username: ${DB_USERNAME:devuser}
    password: ${DB_PASSWORD:devpass}
  flyway:
    url: ${spring.datasource.url}
    user: ${spring.datasource.username}
    password: ${spring.datasource.password}
  jpa:
    hibernate:
      ddl-auto: none # Should not be 'update' with Flyway
    show-sql: false
  # RabbitMQ host already defaults to localhost, so no override needed unless user/pass are set

app:
  storage:
    location: ${user.home}/ecommerce/product-images/local

---
# ===================================================================
# CI PROFILE (ci)
# For Continuous Integration environments (e.g., GitHub Actions)
# ===================================================================
spring:
  config:
    activate:
      on-profile: ci
  datasource:
    url: ****************************************
    username: testuser
    password: ${DATABASE_PASSWORD}
  flyway:
    url: ${spring.datasource.url}
    user: ${spring.datasource.username}
    password: ${spring.datasource.password}
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true

app:
  storage:
    location: ${CI_STORAGE_PATH:/tmp/ecommerce/test-images}
    cleanup-enabled: false

---
# ===================================================================
# PRODUCTION PROFILE (prod)
# ===================================================================
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: ${PROD_DB_URL}
    username: ${PROD_DB_USERNAME}
    password: ${PROD_DB_PASSWORD}
  flyway:
    url: ${spring.datasource.url}
    user: ${spring.datasource.username}
    password: ${spring.datasource.password}
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
  logging:
    level:
      root: INFO
      org.springframework: WARN
      com.rj.ecommerce_backend: INFO

app:
  storage:
    location: ${PROD_STORAGE_PATH}
    base-url: ${CDN_BASE_URL}
    cleanup-schedule: "0 0 2 * * *"  # Daily cleanup at 2 AM
    cleanup-threshold-days: 7