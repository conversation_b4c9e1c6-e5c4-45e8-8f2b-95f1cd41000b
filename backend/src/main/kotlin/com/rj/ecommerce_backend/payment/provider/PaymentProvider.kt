package com.rj.ecommerce_backend.payment.provider

import com.rj.ecommerce.api.shared.messaging.payment.PaymentRequestDTO
import com.rj.ecommerce_backend.order.domain.Order

interface PaymentProvider {
    // Takes an order and returns the specific DTO needed to request a payment from the Payment Microservice.
    fun buildPaymentRequest(order: Order, successUrl: String, cancelUrl: String): PaymentRequestDTO
    fun getProviderName(): PaymentProvider // e.g., "STRIPE", "PAYPAL"
}