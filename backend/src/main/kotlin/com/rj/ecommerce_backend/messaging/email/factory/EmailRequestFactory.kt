package com.rj.ecommerce_backend.messaging.email.factory

import com.rj.ecommerce.api.shared.core.Money
import com.rj.ecommerce.api.shared.dto.customer.CustomerInfoDTO
import com.rj.ecommerce.api.shared.dto.order.MessagingOrderItemDTO
import com.rj.ecommerce.api.shared.enums.Currency
import com.rj.ecommerce.api.shared.enums.EmailTemplate
import com.rj.ecommerce.api.shared.messaging.email.OrderEmailRequestDTO
import com.rj.ecommerce_backend.notification.mapper.OrderNotificationMapper
import com.rj.ecommerce_backend.notification.service.OrderNotificationMapper
import com.rj.ecommerce_backend.order.domain.Order
import com.rj.ecommerce_backend.order.exception.OrderDataInvalidException
import com.rj.ecommerce_backend.user.domain.User
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class EmailRequestFactory(
    private val orderNotificationMapper: OrderNotificationMapper
) {

    fun createOrderConfirmationRequest(order: Order): OrderEmailRequestDTO {
        return createBaseOrderEmailRequest(order, EmailTemplate.ORDER_CONFIRMATION)
    }

    fun createOrderShipmentRequest(order: Order, trackingNumber: String, trackingUrl: String): OrderEmailRequestDTO {
        val additionalData = mapOf(
            "trackingNumber" to trackingNumber,
            "trackingUrl" to trackingUrl
        )
        return createBaseOrderEmailRequest(order, EmailTemplate.ORDER_SHIPMENT, additionalData)
    }


}