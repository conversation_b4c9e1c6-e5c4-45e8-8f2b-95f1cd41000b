package com.rj.payment_service.service

import com.rj.ecommerce.api.shared.enums.PaymentStatus
import com.rj.ecommerce.api.shared.messaging.payment.CheckoutSessionResponseDTO
import com.rj.payment_service.producer.MessageProducer
import com.stripe.model.Charge
import com.stripe.model.StripeObject
import com.stripe.model.checkout.Session
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import javax.management.InvalidAttributeValueException
import kotlin.text.toLong

@Service
class StripeEventWebHookImpl(
    private val messageProducer: MessageProducer,
) : StripeWebhook {

    override fun handleCheckoutSessionCompleted(session: Session?) {
        // here should I throw exception if session is null?
        val validSession: Session = session ?: run {
            // probably should I change this exception type
            throw InvalidAttributeValueException()
        }


        val orderId = session.metadata["orderId"]?.toLong() ?: run {
            // probably should I change this exception type
            throw InvalidAttributeValueException()
        }

        val additionalDetails: MutableMap<String, String> = mutableMapOf()



        val sessionStatus: PaymentStatus = PaymentStatus
            .fromCheckoutSessionStatus(session.status)
        val paymentStatus: PaymentStatus = PaymentStatus
            .fromCheckoutSessionPaymentStatus(session.paymentStatus)

        additionalDetails.put("sessionStatus", sessionStatus.name)

        val response: CheckoutSessionResponseDTO = CheckoutSessionResponseDTO(
            orderId = orderId,
            customerEmail = session.customerEmail,
            sessionId = session.id,
            checkoutUrl = session.successUrl,
            paymentStatus = paymentStatus,
            amountTotal = session.amountTotal,
            currency = session.currency,
            additionalDetails = additionalDetails,
            sessionStatus = TODO(),
            correlationId = TODO(),
            expiresAt = TODO(),
            metadata = TODO(),
        )
        messageProducer.sendCheckoutSessionResponse(
            response,
            session.getId()
        )


    }

    override fun handleCheckoutSessionExpired(session: Session?) {
        // here should I throw exception if session is null?
        val validSession: Session = session ?: run {
            // probably should I change this exception type
            throw InvalidAttributeValueException()
        }


        val orderId = session.metadata["orderId"]?.toLong() ?: run {
            // probably should I change this exception type
            throw InvalidAttributeValueException()
        }

        val additionalDetails: MutableMap<String, String> = mutableMapOf()

        val sessionStatus: PaymentStatus = PaymentStatus
            .fromCheckoutSessionStatus(session.status)
        val paymentStatus: PaymentStatus = PaymentStatus
            .fromCheckoutSessionPaymentStatus(session.paymentStatus)

        val sessionExpiresAt: LocalDateTime = session.expiresAt.let { l ->
            LocalDateTime.ofEpochSecond(session.expiresAt, null)
        }

        additionalDetails.put("sessionStatus", sessionStatus.name)
        additionalDetails.put("sessionExpiresAt", sessionExpiresAt.toString())

        val response: CheckoutSessionResponseDTO = CheckoutSessionResponseDTO(
            orderId = orderId,
            customerEmail = session.customerEmail,
            sessionId = session.id,
            checkoutUrl = session.successUrl,
            paymentStatus = paymentStatus,
            amountTotal = session.amountTotal,
            currency = session.currency,
            additionalDetails = additionalDetails,
        )
        messageProducer.sendCheckoutSessionResponse(
            response,
            session.getId()
        )
    }

    override fun handleChargeSucceeded(charge: Charge?) {
        // here should I throw exception if session is null?
        val validCharge: Charge = charge ?: run {
            // probably should I change this exception type
            throw InvalidAttributeValueException()
        }


        val orderId = charge.metadata["orderId"]?.toLong() ?: run {
            // probably should I change this exception type
            throw InvalidAttributeValueException()
        }

        val additionalDetails: MutableMap<String, String> = mutableMapOf()
        additionalDetails.put("receiptUrl", charge.receiptUrl)
        additionalDetails.put("paymentMethodType", charge.paymentMethod)

        val chargeStatus: PaymentStatus = PaymentStatus
            .fromChargeStatus(charge.status)

        val response: CheckoutSessionResponseDTO = CheckoutSessionResponseDTO(
            orderId = orderId,
            customerEmail = charge.billingDetails.email,
            sessionId = charge.id,
            checkoutUrl = "",
            paymentStatus = chargeStatus,
            amountTotal = charge.amount,
            currency = charge.currency,
            additionalDetails = additionalDetails,
        )
        messageProducer.sendCheckoutSessionResponse(
            response,
            charge.id
        )

    }

    override fun handleUnknownEvent(eventType: String?, `object`: StripeObject?) {
        TODO("Not yet implemented")
    }
}